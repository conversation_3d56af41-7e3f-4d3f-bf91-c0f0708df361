)com.hbg.lib.widgets.LoadingRelativeLayout1com.hbg.module.libkt.custom.indicator.CoIndicator!com.huobi.view.MyNestedScrollView*com.huobi.view.roundview.RoundLinearLayoutcom.ttv.demo.HomeDemoFragment com.ttv.demo.DemoFragmentAdaptercom.ttv.demo.DemoPageFragment'com.ttv.demo.MainActivityWithNavigation com.ttv.demo.PlaceholderFragment(com.ttv.demo.StickyBottomTabDemoActivity;com.ttv.demo.StickyBottomTabDemoActivity.TabFragmentAdapter5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      