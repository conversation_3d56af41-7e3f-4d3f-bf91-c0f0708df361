package com.hbg.module.libkt.custom.indicator

import android.content.Context
import android.graphics.Typeface
import android.os.Build
import android.view.Gravity
import android.widget.RelativeLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.viewpager2.widget.ViewPager2
import com.hbg.lib.common.utils.PixelUtils
import com.hbg.module.libkt.R
import com.hbg.module.libkt.base.ext.dp2px
import com.hbg.module.libkt.base.ext.getAttrColor
import com.hbg.module.libkt.base.ext.toPx
import com.hbg.module.libkt.custom.indicator.navigator.CommonNavigator
import com.hbg.module.libkt.custom.indicator.navigator.adapter.NavigatorAdapter
import com.hbg.module.libkt.custom.indicator.navigator.indicators.LinePagerIndicator
import com.hbg.module.libkt.custom.indicator.navigator.interfaces.IPagerIndicator
import com.hbg.module.libkt.custom.indicator.navigator.interfaces.IPagerTitle
import com.hbg.module.libkt.custom.indicator.navigator.titles.PagerTitleView
import com.hbg.module.libkt.custom.indicator.navigator.titles.RedPointPagerTitleView
import com.hbg.module.libkt.utils.UiTUtils.singleClick
import java.io.Serializable


object IndicatorHelper {

    @JvmStatic
    fun initLineIndicator(
        mContext: Context, tabs: List<TabData>,
        indicator: CoIndicator, vPager: ViewPager2, listener: TabClickListener? = null
    ) {
        initLineIndicator(mContext, tabs, indicator, vPager, Typeface.NORMAL, 14f, 14f, listener)
    }

    @JvmStatic
    fun initLineIndicator(
        mContext: Context, tabs: List<TabData>, indicator: CoIndicator, vPager: ViewPager2,
        titleStyle: Int, normalSize: Float, scaleSize: Float, listener: TabClickListener? = null
    ) {
        initLineIndicator(
            mContext, tabs, indicator, vPager, titleStyle, normalSize, scaleSize,
            R.attr.Functional_Blue, R.attr.Text_L1, listener
        )
    }

    @JvmStatic
    fun initLineIndicator(
        mContext: Context, tabs: List<TabData>, indicator: CoIndicator, vPager: ViewPager2,
        titleStyle: Int, normalSize: Float, scaleSize: Float, selColor: Int = R.attr.Functional_Blue,
        unSelColor: Int = R.attr.Text_L1, listener: TabClickListener? = null
    ) {
        initLineIndicator(mContext, tabs, indicator, vPager, titleStyle, normalSize, scaleSize, selColor, unSelColor, selColor, listener)
    }

    @JvmStatic
    fun initLineIndicator(
        mContext: Context, tabs: List<TabData>, indicator: CoIndicator, vPager: ViewPager2,
        titleStyle: Int, normalSize: Float, scaleSize: Float, selColor: Int = R.attr.Functional_Blue,
        unSelColor: Int = R.attr.Text_L1, indColor: Int = 0, listener: TabClickListener? = null
    ) {
        indicator.setPadding(dp2px(8f),0, dp2px(8f),0)
        val commonNavigator = CommonNavigator(mContext)
        commonNavigator.setAdapter(object : NavigatorAdapter() {
            override fun getCount(): Int {
                return tabs.size
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitle {
                val pagerTitleView = getPageTitle(context, tabs[index].name, normalSize,
                    mContext.getAttrColor(unSelColor),
                    mContext.getAttrColor(
                        if (selColor == R.attr.Functional_Blue) {
                            getSelectColor(context)
                        } else {
                            selColor
                        }
                    )
                )
                pagerTitleView.getPagerTitle()?.setSelectedTextSize(scaleSize)
                pagerTitleView.getPagerTitle()?.setTypeface(null, titleStyle)
                pagerTitleView.singleClick {
                    vPager.setCurrentItem(index, false)
                    listener?.onTabClick(index)
                }
                return pagerTitleView
            }

            override fun getIndicator(context: Context): IPagerIndicator {
                return getNewLineIndicator(context)
            }
        })
        indicator.setNavigator(commonNavigator)
        ViewPagerHelper.bind(indicator, vPager, listener)
    }

    @JvmStatic
    fun initNoLineIndicator(
        mContext: Context, tabs: ArrayList<TabData>,
        indicator: CoIndicator, vPager: ViewPager2, textSize: Float = 14f,
        selColor: Int = R.attr.Text_L1,
        unSelColor: Int = R.attr.Text_L3,
        scaleSize: Float? = null,
        normalFont: Int? = null, scaleFont: Int? = null, isBold: Boolean = false
    ) {
        indicator.setPadding(dp2px(8f),0, dp2px(8f),0)
        val commonNavigator = CommonNavigator(mContext)
        commonNavigator.setAdapter(object : NavigatorAdapter() {
            override fun getCount(): Int {
                return tabs.size
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitle {
                val pagerTitleView = getPageTitle(context, tabs[index].name, textSize,
                    mContext.getAttrColor(unSelColor),
                    mContext.getAttrColor(
                        if (selColor == R.attr.Functional_Blue) {
                            getSelectColor(context)
                        } else {
                            selColor
                        }
                    )
                )
                if (isBold) {
                    pagerTitleView.getPagerTitle()?.typeface = Typeface.DEFAULT_BOLD
                }
                scaleSize?.let { pagerTitleView.getPagerTitle()?.setSelectedTextSize(it) }
                normalFont?.let {
                    pagerTitleView.getPagerTitle()?.run {
                        val typeface = ResourcesCompat.getFont(context, it)
                        setNormalTextFont(typeface)
                    }
                }
                scaleFont?.let {
                    pagerTitleView.getPagerTitle()?.run {
                        val typeface = ResourcesCompat.getFont(context, it)
                        setScaleTextFont(typeface)
                    }
                }
                pagerTitleView.singleClick { vPager.setCurrentItem(index, false) }
                return pagerTitleView
            }

            override fun getIndicator(context: Context): IPagerIndicator? {
                return null
            }
        })
        indicator.setNavigator(commonNavigator)
        ViewPagerHelper.bind(indicator, vPager)
    }

    @JvmStatic
    fun initBottomNoLineIndicator(
        mContext: Context,
        tabs: List<TabData>,
        indicator: CoIndicator,
        indicatorYOffset: Float = 7f,
        vPager: ViewPager2,
        textSize: Float = 14f,
        selColor: Int = R.attr.Text_L1,
        unSelColor: Int = R.attr.Text_L3,
        scaleSize: Float? = null,
        normalFont: Int? = null,
        scaleFont: Int? = null,
        isBold: Boolean = false,
        onTabClick: TabClickListener? = null
    ) {
        indicator.setPadding(dp2px(8f),0, dp2px(8f),0)
        val commonNavigator = CommonNavigator(mContext)
        commonNavigator.setAdapter(object : NavigatorAdapter() {
            override fun getCount(): Int {
                return tabs.size
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitle {
                val convertTextSize = PixelUtils.getDisplayTextSize(context, textSize)
                val pagerTitleView = getPageTitle(context, tabs[index].name, convertTextSize,
                    mContext.getAttrColor(unSelColor),
                    mContext.getAttrColor(
                        if (selColor == R.attr.Functional_Blue) {
                            getSelectColor(context)
                        } else {
                            selColor
                        }
                    )
                )
                if (isBold) {
                    pagerTitleView.getPagerTitle()?.typeface = Typeface.DEFAULT_BOLD
                }
                val typeface = ResourcesCompat.getFont(context, R.font.harmonyos_sans_medium)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    pagerTitleView.getPagerTitle()?.setTypeface(Typeface.create(typeface, 500, false))
                } else {
                    pagerTitleView.getPagerTitle()?.setTypeface(Typeface.create(typeface, Typeface.NORMAL))
                }
                scaleSize?.let {
//                    pagerTitleView.getPagerTitle()?.includeFontPadding = false
//                    pagerTitleView.getPagerTitle()?.height = (it + 4).toPx
//                    pagerTitleView.getPagerTitle()?.gravity = Gravity.BOTTOM
//                    pagerTitleView.getPagerTitle()?.setSelectedTextSize(it)
                }
                normalFont?.let {
                    pagerTitleView.getPagerTitle()?.run {
                        val typeface = ResourcesCompat.getFont(context, it)
                        setNormalTextFont(typeface)
                    }
                }
                scaleFont?.let {
                    pagerTitleView.getPagerTitle()?.run {
                        val typeface = ResourcesCompat.getFont(context, it)
                        setScaleTextFont(typeface)
                    }
                }
                pagerTitleView.singleClick {
                    onTabClick?.onTabClick(index)
                    vPager.setCurrentItem(index, false)
                }
                return pagerTitleView
            }

            override fun getIndicator(context: Context): IPagerIndicator? {
                return getNewLineIndicator(context, yOffset = indicatorYOffset)
            }
        })
        indicator.setNavigator(commonNavigator)
        ViewPagerHelper.bind(indicator, vPager)
    }

    fun getSelectColor(mContext:Context): Int {
        //最顶部TAB选中栏 同行情TAB顶部用白色
        if (mContext != null && "LiveCategoryActivity".equals(mContext.javaClass.simpleName)){
            return R.attr.Text_L1
        }
        return R.attr.Text_L3
    }

    /**
     * 带背景色的
     */
    @JvmStatic
    fun initBgIndicator(
        mContext: Context, tabs: List<TabData>, indicator: CoIndicator,
        vPager: ViewPager2, textSize: Float = 16f, listener: TabClickListener?,
        normalTextColor: Int = R.attr.Text_L3,
        selectedTextColor: Int = getSelectColor(mContext),
        normalBgColor: Int = R.drawable.bg_unsel_indicator,
        selectedBgColor: Int = R.drawable.bg_sel_indicator,
        pLeft: Float = 8f, pTop: Float = 0f, pRight: Float = 8f, pBottom: Float = 0f
    ) {
        val commonNavigator = CommonNavigator(mContext)
        commonNavigator.setAdapter(object : NavigatorAdapter() {
            override fun getCount(): Int {
                return tabs.size
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitle {
                val pagerTitleView = getPageTitle(
                    context, tabs[index].name, textSize,
                    mContext.getAttrColor(normalTextColor),
                    mContext.getAttrColor(selectedTextColor),
                    pLeft, pTop, pRight, pBottom
                )
                (pagerTitleView.getPagerTitle()?.layoutParams as RelativeLayout.LayoutParams?)?.marginEnd = 6f.toPx
                pagerTitleView.getPagerTitle()?.selectedBgColor = selectedBgColor
                pagerTitleView.getPagerTitle()?.normalBgColor = normalBgColor
                pagerTitleView.singleClick {
                    vPager.setCurrentItem(index, false)
                    listener?.onTabClick(index)
                }
                return pagerTitleView
            }

            override fun getIndicator(context: Context): IPagerIndicator? {
                return null
            }
        })
        indicator.setNavigator(commonNavigator)
        ViewPagerHelper.bind(indicator, vPager, listener)
    }

    fun getPageTitle(
        context: Context, name: String?, textSize: Float, normalColor: Int, selectedColor: Int,
        pLeft: Float = 8f, pTop: Float = 0f, pRight: Float = 8f, pBottom: Float = 0f
    ): RedPointPagerTitleView {
        val pagerTitleView = PagerTitleView(context)
        pagerTitleView.layoutParams = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        )
        val redPointPagerTitleView = RedPointPagerTitleView(context)
        redPointPagerTitleView.setPageTitleView(pagerTitleView)
        pagerTitleView.text = name
        pagerTitleView.textSize = textSize
        pagerTitleView.normalColor = normalColor
        pagerTitleView.selectedColor = selectedColor
        pagerTitleView.gravity = Gravity.CENTER
        pagerTitleView.setPadding(pLeft.toPx, pTop.toPx, pRight.toPx, pBottom.toPx)
        return redPointPagerTitleView
    }

    fun getLineIndicator(
        context: Context, lineColor: Int = R.color.baseColorShadeFunctionButtonStart
    ): LinePagerIndicator {
        val pagerIndicator = LinePagerIndicator(context)
        pagerIndicator.setMode(LinePagerIndicator.MODE_EXACTLY)
        pagerIndicator.setColors(
            ContextCompat.getColor(
                context,
                lineColor
            )
        )
        pagerIndicator.setLineWidth(dp2px(20f).toFloat())
        pagerIndicator.setLineHeight(dp2px(2f).toFloat())
        return pagerIndicator
    }

    fun getNewLineIndicator(
        context: Context, yOffset: Float = 7f
    ): LinePagerIndicator {
        val pagerIndicator = LinePagerIndicator(context)
        pagerIndicator.setMode(LinePagerIndicator.MODE_EXACTLY)
        pagerIndicator.setColors(
            context.getAttrColor(R.attr.Button_Blue_Fill)
        )
        pagerIndicator.setLineWidth(dp2px(16f).toFloat())
        pagerIndicator.setLineHeight(dp2px(3f).toFloat())
        pagerIndicator.setYOffset(dp2px(yOffset).toFloat())
        return pagerIndicator
    }



    fun getGapNewLineIndicator(
        context: Context, lineColor: Int = R.color.baseColorShadeFunctionButtonStart
    ): LinePagerIndicator {
        val pagerIndicator = LinePagerIndicator(context)
        pagerIndicator.setMode(LinePagerIndicator.MODE_EXACTLY)
        pagerIndicator.setColors(
            ContextCompat.getColor(
                context,
                lineColor
            )
        )
        pagerIndicator.setLineWidth(dp2px(16f).toFloat())
        pagerIndicator.setLineHeight(dp2px(3f).toFloat())
        pagerIndicator.setYOffset(dp2px(7f).toFloat())
        return pagerIndicator
    }
}

interface TabClickListener {
    fun onTabClick(index: Int)
}

data class TabData(
    val name: String?,
    val type: Int = 0,
    var tabId: Int = 0
) : Serializable