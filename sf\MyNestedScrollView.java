package com.huobi.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.core.view.ViewCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;

import com.hbg.lib.common.utils.DebugLog;

/**
 * Created by wang<PERSON> on 2017/2/8.
 */
public class MyNestedScrollView extends NestedScrollView {

    private int mState = RecyclerView.SCROLL_STATE_IDLE;

    public MyNestedScrollView(Context context) {
        super(context);
        init(context);
    }

    public MyNestedScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public MyNestedScrollView(Context context, AttributeSet attrs,
                              int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {

    }

    private float xDistance, yDistance, lastX, lastY;

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (scrollable) {

            switch (ev.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    xDistance = yDistance = 0f;
                    lastX = ev.getX();
                    lastY = ev.getY();
                    // This is very important line that fixes
                    computeScroll();
                    break;
                case MotionEvent.ACTION_MOVE:
                    final float curX = ev.getX();
                    final float curY = ev.getY();
                    xDistance += Math.abs(curX - lastX);
                    yDistance += Math.abs(curY - lastY);
                    lastX = curX;
                    lastY = curY;
                    if (xDistance > yDistance) {
                        return false;
                    }

            }
            return super.onInterceptTouchEvent(ev);
        }
        return false;
    }

    /**
     * 监听类
     */
    public interface ScrollStateListener {

        /**
         * 滚动状态变化
         *
         * @param state state
         */
        void onStateChanged(int state);

        /**
         * 结束滚动
         */
        void onStopNestedScroll();

        /**
         * 开始滚动
         */
        void onStartNestedScroll();
    }

    /**
     * 滚动状态监听
     *
     * @param scrollListener ScrollStateListener
     */
    public void setScrollStateListener(ScrollStateListener scrollListener) {
        this.mScrollListener = scrollListener;
    }

    private ScrollStateListener mScrollListener;
    private OnScrollChangedListener mOnScrollChangedListener;
    private int mTouchType = ViewCompat.TYPE_TOUCH;
    private boolean mIsStartScroll = false;

    @Override
    public boolean startNestedScroll(int axes, int type) {
        mTouchType = type;
        if (mScrollListener != null && !mIsStartScroll) {
            mIsStartScroll = true;
            mScrollListener.onStartNestedScroll();
        }
        return super.startNestedScroll(axes, type);
    }

    @Override
    public void stopNestedScroll(int type) {
        super.stopNestedScroll(type);
        dispatchScrollState(RecyclerView.SCROLL_STATE_IDLE);
        if (mScrollListener != null && mTouchType == type && mIsStartScroll) {
            mIsStartScroll = false;
            mScrollListener.onStopNestedScroll();
        }
    }

    @Override
    public boolean onStartNestedScroll(View child, View target, int nestedScrollAxes) {
        dispatchScrollState(RecyclerView.SCROLL_STATE_DRAGGING);
        return super.onStartNestedScroll(child, target, nestedScrollAxes);
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (mOnScrollChangedListener != null) {
            mOnScrollChangedListener.onScrollChange(this, l, t, oldl, oldt);
        }
    }

    /**
     * 设置监听
     *
     * @param l OnScrollChangedListener
     */
    public void setOnScrollChangedListener(OnScrollChangedListener l) {
        mOnScrollChangedListener = l;
    }

    public interface OnScrollChangedListener {
        /**
         * 发生滚动回高雷
         *
         * @param v          NestedScrollView
         * @param scrollX    scrollX
         * @param scrollY    scrollY
         * @param oldScrollX oldScrollX
         * @param oldScrollY oldScrollY
         */
        void onScrollChange(NestedScrollView v, int scrollX, int scrollY,
                            int oldScrollX, int oldScrollY);
    }

    @Override
    public boolean startNestedScroll(int axes) {
        boolean superScroll = super.startNestedScroll(axes);
        dispatchScrollState(RecyclerView.SCROLL_STATE_DRAGGING);
        return superScroll;
    }


    /**
     * 分发滚动状态
     *
     * @param state int
     */
    private void dispatchScrollState(int state) {
        if (mScrollListener != null && mState != state) {
            mScrollListener.onStateChanged(state);
            DebugLog.d("----Unsmooth----");
            mState = state;
        }
    }

    private boolean scrollable = true;


    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        return scrollable && super.onTouchEvent(ev);
    }

    /**
     * 设置是否可滚动  默认可以滚动不影响其他使用的地方
     *
     * @param enabled 可以滚动
     */
    public void setScrollingEnabled(boolean enabled) {
        scrollable = enabled;
    }

}