  darker_gray android.R.color  white android.R.color  Activity android.app  ARG_POSITION android.app.Activity  	ARG_TITLE android.app.Activity  Bundle android.app.Activity  DemoFragment android.app.Activity  DynamicTabController android.app.Activity  	Exception android.app.Activity  HomeDemoFragment android.app.Activity  Int android.app.Activity  Log android.app.Activity  PlaceholderFragment android.app.Activity  R android.app.Activity  TAG android.app.Activity  TabFragmentAdapter android.app.Activity  TabLayoutMediator android.app.Activity  TextView android.app.Activity  View android.app.Activity  
ViewPager2 android.app.Activity  android android.app.Activity  dynamicTabController android.app.Activity  findViewById android.app.Activity  	getOrNull android.app.Activity  indices android.app.Activity  listOf android.app.Activity  newInstance android.app.Activity  onCreate android.app.Activity  OnPageChangeCallback android.app.Activity.ViewPager2  view android.app.Activity.android  widget android.app.Activity.android  	ViewGroup !android.app.Activity.android.view  	ImageView #android.app.Activity.android.widget  TextView #android.app.Activity.android.widget  Context android.content  ARG_POSITION android.content.Context  	ARG_TITLE android.content.Context  Bundle android.content.Context  DemoFragment android.content.Context  DynamicTabController android.content.Context  	Exception android.content.Context  HomeDemoFragment android.content.Context  Int android.content.Context  Log android.content.Context  PlaceholderFragment android.content.Context  R android.content.Context  TAG android.content.Context  TabFragmentAdapter android.content.Context  TabLayoutMediator android.content.Context  TextView android.content.Context  View android.content.Context  
ViewPager2 android.content.Context  android android.content.Context  dynamicTabController android.content.Context  getColor android.content.Context  	getOrNull android.content.Context  indices android.content.Context  listOf android.content.Context  newInstance android.content.Context  obtainStyledAttributes android.content.Context  	resources android.content.Context  OnPageChangeCallback "android.content.Context.ViewPager2  view android.content.Context.android  widget android.content.Context.android  	ViewGroup $android.content.Context.android.view  	ImageView &android.content.Context.android.widget  TextView &android.content.Context.android.widget  ARG_POSITION android.content.ContextWrapper  	ARG_TITLE android.content.ContextWrapper  Bundle android.content.ContextWrapper  DemoFragment android.content.ContextWrapper  DynamicTabController android.content.ContextWrapper  	Exception android.content.ContextWrapper  HomeDemoFragment android.content.ContextWrapper  Int android.content.ContextWrapper  Log android.content.ContextWrapper  PlaceholderFragment android.content.ContextWrapper  R android.content.ContextWrapper  TAG android.content.ContextWrapper  TabFragmentAdapter android.content.ContextWrapper  TabLayoutMediator android.content.ContextWrapper  TextView android.content.ContextWrapper  View android.content.ContextWrapper  
ViewPager2 android.content.ContextWrapper  android android.content.ContextWrapper  dynamicTabController android.content.ContextWrapper  	getOrNull android.content.ContextWrapper  indices android.content.ContextWrapper  listOf android.content.ContextWrapper  newInstance android.content.ContextWrapper  OnPageChangeCallback )android.content.ContextWrapper.ViewPager2  view &android.content.ContextWrapper.android  widget &android.content.ContextWrapper.android  	ViewGroup +android.content.ContextWrapper.android.view  	ImageView -android.content.ContextWrapper.android.widget  TextView -android.content.ContextWrapper.android.widget  
Configuration android.content.res  
TypedArray android.content.res  orientation !android.content.res.Configuration  displayMetrics android.content.res.Resources  getDimensionPixelSize android.content.res.Resources  
getIdentifier android.content.res.Resources  
getBoolean android.content.res.TypedArray  getColor android.content.res.TypedArray  getDimension android.content.res.TypedArray  recycle android.content.res.TypedArray  Canvas android.graphics  Paint android.graphics  Path android.graphics  RectF android.graphics  clipPath android.graphics.Canvas  
drawRoundRect android.graphics.Canvas  ANTI_ALIAS_FLAG android.graphics.Paint  Style android.graphics.Paint  color android.graphics.Paint  style android.graphics.Paint  FILL android.graphics.Paint.Style  	Direction android.graphics.Path  addRoundRect android.graphics.Path  reset android.graphics.Path  CW android.graphics.Path.Direction  set android.graphics.RectF  Bundle 
android.os  getInt android.os.BaseBundle  	getString android.os.BaseBundle  putInt android.os.BaseBundle  	putString android.os.BaseBundle  getInt android.os.Bundle  	getString android.os.Bundle  putInt android.os.Bundle  	putString android.os.Bundle  AttributeSet android.util  Log android.util  let android.util.AttributeSet  density android.util.DisplayMetrics  heightPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  i android.util.Log  v android.util.Log  w android.util.Log  Gravity android.view  LayoutInflater android.view  View android.view  	ViewGroup android.view  ViewPropertyAnimator android.view  ARG_POSITION  android.view.ContextThemeWrapper  	ARG_TITLE  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  DemoFragment  android.view.ContextThemeWrapper  DynamicTabController  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  HomeDemoFragment  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  PlaceholderFragment  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  TabFragmentAdapter  android.view.ContextThemeWrapper  TabLayoutMediator  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  
ViewPager2  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  dynamicTabController  android.view.ContextThemeWrapper  	getOrNull  android.view.ContextThemeWrapper  indices  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  newInstance  android.view.ContextThemeWrapper  OnPageChangeCallback +android.view.ContextThemeWrapper.ViewPager2  view (android.view.ContextThemeWrapper.android  widget (android.view.ContextThemeWrapper.android  	ViewGroup -android.view.ContextThemeWrapper.android.view  	ImageView /android.view.ContextThemeWrapper.android.widget  TextView /android.view.ContextThemeWrapper.android.widget  BOTTOM android.view.Gravity  CENTER android.view.Gravity  inflate android.view.LayoutInflater  GONE android.view.View  
GRAVITY_START android.view.View  LAYER_TYPE_SOFTWARE android.view.View  LinearLayout android.view.View  MODE_SCROLLABLE android.view.View  OnClickListener android.view.View  OnTabSelectedListener android.view.View  Paint android.view.View  Path android.view.View  R android.view.View  RectF android.view.View  Tab android.view.View  VISIBLE android.view.View  alpha android.view.View  android android.view.View  animate android.view.View  apply android.view.View  context android.view.View  	elevation android.view.View  findViewById android.view.View  getLocationOnScreen android.view.View  height android.view.View  layoutParams android.view.View  let android.view.View  paddingLeft android.view.View  paddingRight android.view.View  
paddingTop android.view.View  parent android.view.View  post android.view.View  scrollY android.view.View  setBackgroundColor android.view.View  setLayerType android.view.View  setOnClickListener android.view.View  
setPadding android.view.View  translationY android.view.View  
visibility android.view.View  width android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  
GRAVITY_START android.view.ViewGroup  LAYER_TYPE_SOFTWARE android.view.ViewGroup  MODE_SCROLLABLE android.view.ViewGroup  OnTabSelectedListener android.view.ViewGroup  Paint android.view.ViewGroup  Path android.view.ViewGroup  R android.view.ViewGroup  RectF android.view.ViewGroup  Tab android.view.ViewGroup  addView android.view.ViewGroup  android android.view.ViewGroup  
getChildAt android.view.ViewGroup  let android.view.ViewGroup  
removeView android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  alpha !android.view.ViewPropertyAnimator  setDuration !android.view.ViewPropertyAnimator  start !android.view.ViewPropertyAnimator  translationY !android.view.ViewPropertyAnimator  
withEndAction !android.view.ViewPropertyAnimator  FrameLayout android.widget  	ImageView android.widget  LinearLayout android.widget  RelativeLayout android.widget  TextView android.widget  
GRAVITY_START android.widget.FrameLayout  MODE_SCROLLABLE android.widget.FrameLayout  OnTabSelectedListener android.widget.FrameLayout  Tab android.widget.FrameLayout  android android.widget.FrameLayout  let android.widget.FrameLayout  
GRAVITY_START #android.widget.HorizontalScrollView  MODE_SCROLLABLE #android.widget.HorizontalScrollView  OnTabSelectedListener #android.widget.HorizontalScrollView  Tab #android.widget.HorizontalScrollView  let #android.widget.HorizontalScrollView  setColorFilter android.widget.ImageView  LAYER_TYPE_SOFTWARE android.widget.LinearLayout  LayoutParams android.widget.LinearLayout  LinearLayout android.widget.LinearLayout  Paint android.widget.LinearLayout  Path android.widget.LinearLayout  R android.widget.LinearLayout  RectF android.widget.LinearLayout  VERTICAL android.widget.LinearLayout  View android.widget.LinearLayout  addView android.widget.LinearLayout  alpha android.widget.LinearLayout  android android.widget.LinearLayout  animate android.widget.LinearLayout  apply android.widget.LinearLayout  context android.widget.LinearLayout  	elevation android.widget.LinearLayout  height android.widget.LinearLayout  let android.widget.LinearLayout  onDraw android.widget.LinearLayout  orientation android.widget.LinearLayout  paddingLeft android.widget.LinearLayout  paddingRight android.widget.LinearLayout  
paddingTop android.widget.LinearLayout  parent android.widget.LinearLayout  post android.widget.LinearLayout  setBackgroundColor android.widget.LinearLayout  
setPadding android.widget.LinearLayout  translationY android.widget.LinearLayout  
visibility android.widget.LinearLayout  MATCH_PARENT (android.widget.LinearLayout.LayoutParams  android android.widget.TextView  apply android.widget.TextView  gravity android.widget.TextView  setBackgroundColor android.widget.TextView  
setPadding android.widget.TextView  setTextColor android.widget.TextView  text android.widget.TextView  textSize android.widget.TextView  
trimIndent android.widget.TextView  ARG_POSITION #androidx.activity.ComponentActivity  	ARG_TITLE #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  DemoFragment #androidx.activity.ComponentActivity  DynamicTabController #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  HomeDemoFragment #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  PlaceholderFragment #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  TabFragmentAdapter #androidx.activity.ComponentActivity  TabLayoutMediator #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  
ViewPager2 #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  dynamicTabController #androidx.activity.ComponentActivity  	getOrNull #androidx.activity.ComponentActivity  indices #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  newInstance #androidx.activity.ComponentActivity  OnPageChangeCallback .androidx.activity.ComponentActivity.ViewPager2  view +androidx.activity.ComponentActivity.android  widget +androidx.activity.ComponentActivity.android  	ViewGroup 0androidx.activity.ComponentActivity.android.view  	ImageView 2androidx.activity.ComponentActivity.android.widget  TextView 2androidx.activity.ComponentActivity.android.widget  AppCompatActivity androidx.appcompat.app  ARG_POSITION (androidx.appcompat.app.AppCompatActivity  	ARG_TITLE (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  DemoFragment (androidx.appcompat.app.AppCompatActivity  DynamicTabController (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  HomeDemoFragment (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  PlaceholderFragment (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  TAG (androidx.appcompat.app.AppCompatActivity  TabFragmentAdapter (androidx.appcompat.app.AppCompatActivity  TabLayoutMediator (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  
ViewPager2 (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  dynamicTabController (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  	getOrNull (androidx.appcompat.app.AppCompatActivity  indices (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  newInstance (androidx.appcompat.app.AppCompatActivity  onConfigurationChanged (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onPause (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  	resources (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  OnPageChangeCallback 3androidx.appcompat.app.AppCompatActivity.ViewPager2  view 0androidx.appcompat.app.AppCompatActivity.android  widget 0androidx.appcompat.app.AppCompatActivity.android  	ViewGroup 5androidx.appcompat.app.AppCompatActivity.android.view  	ImageView 7androidx.appcompat.app.AppCompatActivity.android.widget  TextView 7androidx.appcompat.app.AppCompatActivity.android.widget  CoordinatorLayout !androidx.coordinatorlayout.widget  LayoutParams 3androidx.coordinatorlayout.widget.CoordinatorLayout  addView 3androidx.coordinatorlayout.widget.CoordinatorLayout  findViewById 3androidx.coordinatorlayout.widget.CoordinatorLayout  Gravity @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  MATCH_PARENT @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  WRAP_CONTENT @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  apply @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  gravity @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  ARG_POSITION #androidx.core.app.ComponentActivity  	ARG_TITLE #androidx.core.app.ComponentActivity  AppBarLayout #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CoIndicator #androidx.core.app.ComponentActivity  CoordinatorLayout #androidx.core.app.ComponentActivity  DemoFragment #androidx.core.app.ComponentActivity  DynamicTabController #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Fragment #androidx.core.app.ComponentActivity  FragmentActivity #androidx.core.app.ComponentActivity  FragmentStateAdapter #androidx.core.app.ComponentActivity  HomeDemoFragment #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  LayoutInflater #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  NonInvasiveDynamicTabController #androidx.core.app.ComponentActivity  PlaceholderFragment #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  TabFragmentAdapter #androidx.core.app.ComponentActivity  TabLayoutMediator #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  	ViewGroup #androidx.core.app.ComponentActivity  
ViewPager2 #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  dynamicTabController #androidx.core.app.ComponentActivity  	getOrNull #androidx.core.app.ComponentActivity  indices #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  newInstance #androidx.core.app.ComponentActivity  OnPageChangeCallback .androidx.core.app.ComponentActivity.ViewPager2  content +androidx.core.app.ComponentActivity.android  view +androidx.core.app.ComponentActivity.android  widget +androidx.core.app.ComponentActivity.android  res 3androidx.core.app.ComponentActivity.android.content  
Configuration 7androidx.core.app.ComponentActivity.android.content.res  	ViewGroup 0androidx.core.app.ComponentActivity.android.view  	ImageView 2androidx.core.app.ComponentActivity.android.widget  TextView 2androidx.core.app.ComponentActivity.android.widget  NestedScrollView androidx.core.widget  android %androidx.core.widget.NestedScrollView  onNestedScroll %androidx.core.widget.NestedScrollView  onScrollChanged %androidx.core.widget.NestedScrollView  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  FragmentManager androidx.fragment.app  FragmentTransaction androidx.fragment.app  ARG_POSITION androidx.fragment.app.Fragment  	ARG_TITLE androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  DemoFragment androidx.fragment.app.Fragment  DemoFragmentAdapter androidx.fragment.app.Fragment  DemoPageFragment androidx.fragment.app.Fragment  DynamicTabController androidx.fragment.app.Fragment  	Exception androidx.fragment.app.Fragment  Int androidx.fragment.app.Fragment  Log androidx.fragment.app.Fragment  PlaceholderFragment androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  TAG androidx.fragment.app.Fragment  TabLayoutMediator androidx.fragment.app.Fragment  TextView androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  
ViewPager2 androidx.fragment.app.Fragment  activity androidx.fragment.app.Fragment  android androidx.fragment.app.Fragment  apply androidx.fragment.app.Fragment  	arguments androidx.fragment.app.Fragment  dynamicTabController androidx.fragment.app.Fragment  	getOrNull androidx.fragment.app.Fragment  listOf androidx.fragment.app.Fragment  onConfigurationChanged androidx.fragment.app.Fragment  
onDestroyView androidx.fragment.app.Fragment  onPause androidx.fragment.app.Fragment  onResume androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  
trimIndent androidx.fragment.app.Fragment  view androidx.fragment.app.Fragment  OnPageChangeCallback )androidx.fragment.app.Fragment.ViewPager2  ARG_POSITION &androidx.fragment.app.FragmentActivity  	ARG_TITLE &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  DemoFragment &androidx.fragment.app.FragmentActivity  DynamicTabController &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  HomeDemoFragment &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  PlaceholderFragment &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  TAG &androidx.fragment.app.FragmentActivity  TabFragmentAdapter &androidx.fragment.app.FragmentActivity  TabLayoutMediator &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  
ViewPager2 &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  dynamicTabController &androidx.fragment.app.FragmentActivity  	getOrNull &androidx.fragment.app.FragmentActivity  indices &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  newInstance &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onPause &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  supportFragmentManager &androidx.fragment.app.FragmentActivity  OnPageChangeCallback 1androidx.fragment.app.FragmentActivity.ViewPager2  view .androidx.fragment.app.FragmentActivity.android  widget .androidx.fragment.app.FragmentActivity.android  	ViewGroup 3androidx.fragment.app.FragmentActivity.android.view  	ImageView 5androidx.fragment.app.FragmentActivity.android.widget  TextView 5androidx.fragment.app.FragmentActivity.android.widget  beginTransaction %androidx.fragment.app.FragmentManager  commit )androidx.fragment.app.FragmentTransaction  replace )androidx.fragment.app.FragmentTransaction  DemoFragment 1androidx.recyclerview.widget.RecyclerView.Adapter  DemoPageFragment 1androidx.recyclerview.widget.RecyclerView.Adapter  newInstance 1androidx.recyclerview.widget.RecyclerView.Adapter  FragmentStateAdapter androidx.viewpager2.adapter  DemoFragment 0androidx.viewpager2.adapter.FragmentStateAdapter  DemoPageFragment 0androidx.viewpager2.adapter.FragmentStateAdapter  newInstance 0androidx.viewpager2.adapter.FragmentStateAdapter  
ViewPager2 androidx.viewpager2.widget  OnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  adapter %androidx.viewpager2.widget.ViewPager2  registerOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  Log :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  TAG :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  dynamicTabController :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageSelected :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  AppBarLayout "com.google.android.material.appbar  OnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  addOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  removeOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  totalScrollRange /com.google.android.material.appbar.AppBarLayout  let Gcom.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener  	TabLayout  com.google.android.material.tabs  TabLayoutMediator  com.google.android.material.tabs  
GRAVITY_START *com.google.android.material.tabs.TabLayout  LinearLayout *com.google.android.material.tabs.TabLayout  MODE_SCROLLABLE *com.google.android.material.tabs.TabLayout  OnTabSelectedListener *com.google.android.material.tabs.TabLayout  R *com.google.android.material.tabs.TabLayout  Tab *com.google.android.material.tabs.TabLayout  	TabLayout *com.google.android.material.tabs.TabLayout  addOnTabSelectedListener *com.google.android.material.tabs.TabLayout  addTab *com.google.android.material.tabs.TabLayout  android *com.google.android.material.tabs.TabLayout  apply *com.google.android.material.tabs.TabLayout  context *com.google.android.material.tabs.TabLayout  layoutParams *com.google.android.material.tabs.TabLayout  let *com.google.android.material.tabs.TabLayout  newTab *com.google.android.material.tabs.TabLayout  setBackgroundColor *com.google.android.material.tabs.TabLayout  setOnTabSelectedListener *com.google.android.material.tabs.TabLayout  
tabGravity *com.google.android.material.tabs.TabLayout  tabMode *com.google.android.material.tabs.TabLayout  let .com.google.android.material.tabs.TabLayout.Tab  position .com.google.android.material.tabs.TabLayout.Tab  text .com.google.android.material.tabs.TabLayout.Tab  TabConfigurationStrategy 2com.google.android.material.tabs.TabLayoutMediator  attach 2com.google.android.material.tabs.TabLayoutMediator  detach 2com.google.android.material.tabs.TabLayoutMediator  <SAM-CONSTRUCTOR> Kcom.google.android.material.tabs.TabLayoutMediator.TabConfigurationStrategy  AttributeSet com.hbg.lib.widgets  Context com.hbg.lib.widgets  Int com.hbg.lib.widgets  JvmOverloads com.hbg.lib.widgets  LoadingRelativeLayout com.hbg.lib.widgets  RelativeLayout com.hbg.lib.widgets  AttributeSet %com.hbg.module.libkt.custom.indicator  CoIndicator %com.hbg.module.libkt.custom.indicator  Context %com.hbg.module.libkt.custom.indicator  
GRAVITY_START %com.hbg.module.libkt.custom.indicator  Int %com.hbg.module.libkt.custom.indicator  JvmOverloads %com.hbg.module.libkt.custom.indicator  MODE_SCROLLABLE %com.hbg.module.libkt.custom.indicator  OnTabSelectedListener %com.hbg.module.libkt.custom.indicator  String %com.hbg.module.libkt.custom.indicator  Tab %com.hbg.module.libkt.custom.indicator  	TabLayout %com.hbg.module.libkt.custom.indicator  Unit %com.hbg.module.libkt.custom.indicator  let %com.hbg.module.libkt.custom.indicator  
GRAVITY_START 1com.hbg.module.libkt.custom.indicator.CoIndicator  MODE_SCROLLABLE 1com.hbg.module.libkt.custom.indicator.CoIndicator  addOnTabSelectedListener 1com.hbg.module.libkt.custom.indicator.CoIndicator  addTab 1com.hbg.module.libkt.custom.indicator.CoIndicator  getLocationOnScreen 1com.hbg.module.libkt.custom.indicator.CoIndicator  height 1com.hbg.module.libkt.custom.indicator.CoIndicator  let 1com.hbg.module.libkt.custom.indicator.CoIndicator  newTab 1com.hbg.module.libkt.custom.indicator.CoIndicator  setOnTabSelectedListener 1com.hbg.module.libkt.custom.indicator.CoIndicator  
tabGravity 1com.hbg.module.libkt.custom.indicator.CoIndicator  tabMode 1com.hbg.module.libkt.custom.indicator.CoIndicator  AttributeSet com.huobi.view  Context com.huobi.view  Int com.huobi.view  JvmOverloads com.huobi.view  MyNestedScrollView com.huobi.view  NestedScrollView com.huobi.view  android com.huobi.view  android !com.huobi.view.MyNestedScrollView  scrollY !com.huobi.view.MyNestedScrollView  view com.huobi.view.android  View com.huobi.view.android.view  AttributeSet com.huobi.view.roundview  Canvas com.huobi.view.roundview  Context com.huobi.view.roundview  Int com.huobi.view.roundview  JvmOverloads com.huobi.view.roundview  LAYER_TYPE_SOFTWARE com.huobi.view.roundview  LinearLayout com.huobi.view.roundview  Paint com.huobi.view.roundview  Path com.huobi.view.roundview  R com.huobi.view.roundview  RectF com.huobi.view.roundview  RoundLinearLayout com.huobi.view.roundview  let com.huobi.view.roundview  LAYER_TYPE_SOFTWARE *com.huobi.view.roundview.RoundLinearLayout  Paint *com.huobi.view.roundview.RoundLinearLayout  Path *com.huobi.view.roundview.RoundLinearLayout  R *com.huobi.view.roundview.RoundLinearLayout  RectF *com.huobi.view.roundview.RoundLinearLayout  backgroundColor *com.huobi.view.roundview.RoundLinearLayout  cornerRadius *com.huobi.view.roundview.RoundLinearLayout  height *com.huobi.view.roundview.RoundLinearLayout  isRippleEnable *com.huobi.view.roundview.RoundLinearLayout  let *com.huobi.view.roundview.RoundLinearLayout  paint *com.huobi.view.roundview.RoundLinearLayout  path *com.huobi.view.roundview.RoundLinearLayout  rectF *com.huobi.view.roundview.RoundLinearLayout  setLayerType *com.huobi.view.roundview.RoundLinearLayout  width *com.huobi.view.roundview.RoundLinearLayout  ARG_POSITION com.ttv.demo  	ARG_TITLE com.ttv.demo  AppBarLayout com.ttv.demo  AppCompatActivity com.ttv.demo  Boolean com.ttv.demo  Bundle com.ttv.demo  CoIndicator com.ttv.demo  CoordinatorLayout com.ttv.demo  DemoFragment com.ttv.demo  DemoFragmentAdapter com.ttv.demo  DemoPageFragment com.ttv.demo  DynamicTabController com.ttv.demo  	Exception com.ttv.demo  Fragment com.ttv.demo  FragmentActivity com.ttv.demo  FragmentStateAdapter com.ttv.demo  HomeDemoFragment com.ttv.demo  Int com.ttv.demo  LayoutInflater com.ttv.demo  List com.ttv.demo  Log com.ttv.demo  MainActivityWithNavigation com.ttv.demo  NonInvasiveDynamicTabController com.ttv.demo  PlaceholderFragment com.ttv.demo  R com.ttv.demo  StickyBottomTabDemoActivity com.ttv.demo  String com.ttv.demo  TAG com.ttv.demo  TabFragmentAdapter com.ttv.demo  TabLayoutMediator com.ttv.demo  TextView com.ttv.demo  View com.ttv.demo  	ViewGroup com.ttv.demo  
ViewPager2 com.ttv.demo  android com.ttv.demo  androidx com.ttv.demo  apply com.ttv.demo  dynamicTabController com.ttv.demo  	getOrNull com.ttv.demo  indices com.ttv.demo  listOf com.ttv.demo  newInstance com.ttv.demo  
trimIndent com.ttv.demo  DemoPageFragment  com.ttv.demo.DemoFragmentAdapter  newInstance  com.ttv.demo.DemoFragmentAdapter  	tabTitles  com.ttv.demo.DemoFragmentAdapter  ARG_POSITION com.ttv.demo.DemoPageFragment  	ARG_TITLE com.ttv.demo.DemoPageFragment  Bundle com.ttv.demo.DemoPageFragment  	Companion com.ttv.demo.DemoPageFragment  DemoPageFragment com.ttv.demo.DemoPageFragment  Int com.ttv.demo.DemoPageFragment  LayoutInflater com.ttv.demo.DemoPageFragment  String com.ttv.demo.DemoPageFragment  TextView com.ttv.demo.DemoPageFragment  View com.ttv.demo.DemoPageFragment  	ViewGroup com.ttv.demo.DemoPageFragment  android com.ttv.demo.DemoPageFragment  apply com.ttv.demo.DemoPageFragment  	arguments com.ttv.demo.DemoPageFragment  newInstance com.ttv.demo.DemoPageFragment  requireContext com.ttv.demo.DemoPageFragment  
trimIndent com.ttv.demo.DemoPageFragment  ARG_POSITION 'com.ttv.demo.DemoPageFragment.Companion  	ARG_TITLE 'com.ttv.demo.DemoPageFragment.Companion  Bundle 'com.ttv.demo.DemoPageFragment.Companion  DemoPageFragment 'com.ttv.demo.DemoPageFragment.Companion  TextView 'com.ttv.demo.DemoPageFragment.Companion  android 'com.ttv.demo.DemoPageFragment.Companion  apply 'com.ttv.demo.DemoPageFragment.Companion  newInstance 'com.ttv.demo.DemoPageFragment.Companion  
trimIndent 'com.ttv.demo.DemoPageFragment.Companion  AppBarLayout com.ttv.demo.HomeDemoFragment  Bundle com.ttv.demo.HomeDemoFragment  CoIndicator com.ttv.demo.HomeDemoFragment  CoordinatorLayout com.ttv.demo.HomeDemoFragment  DemoFragmentAdapter com.ttv.demo.HomeDemoFragment  DynamicTabController com.ttv.demo.HomeDemoFragment  	Exception com.ttv.demo.HomeDemoFragment  Int com.ttv.demo.HomeDemoFragment  LayoutInflater com.ttv.demo.HomeDemoFragment  Log com.ttv.demo.HomeDemoFragment  R com.ttv.demo.HomeDemoFragment  TAG com.ttv.demo.HomeDemoFragment  TabLayoutMediator com.ttv.demo.HomeDemoFragment  TextView com.ttv.demo.HomeDemoFragment  View com.ttv.demo.HomeDemoFragment  	ViewGroup com.ttv.demo.HomeDemoFragment  
ViewPager2 com.ttv.demo.HomeDemoFragment  android com.ttv.demo.HomeDemoFragment  appBarLayout com.ttv.demo.HomeDemoFragment  apply com.ttv.demo.HomeDemoFragment  coIndicator com.ttv.demo.HomeDemoFragment  coordinatorLayout com.ttv.demo.HomeDemoFragment  dynamicTabController com.ttv.demo.HomeDemoFragment  	getOrNull com.ttv.demo.HomeDemoFragment  	initViews com.ttv.demo.HomeDemoFragment  listOf com.ttv.demo.HomeDemoFragment  requireContext com.ttv.demo.HomeDemoFragment  setupBusinessListeners com.ttv.demo.HomeDemoFragment  setupDynamicTabController com.ttv.demo.HomeDemoFragment  setupFluentContainer com.ttv.demo.HomeDemoFragment  setupViewPager com.ttv.demo.HomeDemoFragment  	tabTitles com.ttv.demo.HomeDemoFragment  view com.ttv.demo.HomeDemoFragment  	viewPager com.ttv.demo.HomeDemoFragment  DemoFragmentAdapter 'com.ttv.demo.HomeDemoFragment.Companion  DynamicTabController 'com.ttv.demo.HomeDemoFragment.Companion  Log 'com.ttv.demo.HomeDemoFragment.Companion  R 'com.ttv.demo.HomeDemoFragment.Companion  TAG 'com.ttv.demo.HomeDemoFragment.Companion  TabLayoutMediator 'com.ttv.demo.HomeDemoFragment.Companion  TextView 'com.ttv.demo.HomeDemoFragment.Companion  apply 'com.ttv.demo.HomeDemoFragment.Companion  dynamicTabController 'com.ttv.demo.HomeDemoFragment.Companion  	getOrNull 'com.ttv.demo.HomeDemoFragment.Companion  listOf 'com.ttv.demo.HomeDemoFragment.Companion  OnPageChangeCallback (com.ttv.demo.HomeDemoFragment.ViewPager2  content %com.ttv.demo.HomeDemoFragment.android  res -com.ttv.demo.HomeDemoFragment.android.content  
Configuration 1com.ttv.demo.HomeDemoFragment.android.content.res  Boolean 'com.ttv.demo.MainActivityWithNavigation  Bundle 'com.ttv.demo.MainActivityWithNavigation  HomeDemoFragment 'com.ttv.demo.MainActivityWithNavigation  Int 'com.ttv.demo.MainActivityWithNavigation  Log 'com.ttv.demo.MainActivityWithNavigation  PlaceholderFragment 'com.ttv.demo.MainActivityWithNavigation  R 'com.ttv.demo.MainActivityWithNavigation  String 'com.ttv.demo.MainActivityWithNavigation  TAG 'com.ttv.demo.MainActivityWithNavigation  View 'com.ttv.demo.MainActivityWithNavigation  android 'com.ttv.demo.MainActivityWithNavigation  findViewById 'com.ttv.demo.MainActivityWithNavigation  getColor 'com.ttv.demo.MainActivityWithNavigation  	initViews 'com.ttv.demo.MainActivityWithNavigation  loadHomeFragment 'com.ttv.demo.MainActivityWithNavigation  loadPlaceholderFragment 'com.ttv.demo.MainActivityWithNavigation  newInstance 'com.ttv.demo.MainActivityWithNavigation  resetAllTabs 'com.ttv.demo.MainActivityWithNavigation  	selectTab 'com.ttv.demo.MainActivityWithNavigation  setContentView 'com.ttv.demo.MainActivityWithNavigation  setTabSelected 'com.ttv.demo.MainActivityWithNavigation  setupBottomNavigation 'com.ttv.demo.MainActivityWithNavigation  supportFragmentManager 'com.ttv.demo.MainActivityWithNavigation  	tabAssets 'com.ttv.demo.MainActivityWithNavigation  tabHome 'com.ttv.demo.MainActivityWithNavigation  	tabMarket 'com.ttv.demo.MainActivityWithNavigation  
tabProfile 'com.ttv.demo.MainActivityWithNavigation  tabTrade 'com.ttv.demo.MainActivityWithNavigation  HomeDemoFragment 1com.ttv.demo.MainActivityWithNavigation.Companion  Log 1com.ttv.demo.MainActivityWithNavigation.Companion  PlaceholderFragment 1com.ttv.demo.MainActivityWithNavigation.Companion  R 1com.ttv.demo.MainActivityWithNavigation.Companion  TAG 1com.ttv.demo.MainActivityWithNavigation.Companion  newInstance 1com.ttv.demo.MainActivityWithNavigation.Companion  view /com.ttv.demo.MainActivityWithNavigation.android  widget /com.ttv.demo.MainActivityWithNavigation.android  	ViewGroup 4com.ttv.demo.MainActivityWithNavigation.android.view  	ImageView 6com.ttv.demo.MainActivityWithNavigation.android.widget  TextView 6com.ttv.demo.MainActivityWithNavigation.android.widget  	ARG_TITLE  com.ttv.demo.PlaceholderFragment  Bundle  com.ttv.demo.PlaceholderFragment  	Companion  com.ttv.demo.PlaceholderFragment  PlaceholderFragment  com.ttv.demo.PlaceholderFragment  String  com.ttv.demo.PlaceholderFragment  View  com.ttv.demo.PlaceholderFragment  android  com.ttv.demo.PlaceholderFragment  apply  com.ttv.demo.PlaceholderFragment  	arguments  com.ttv.demo.PlaceholderFragment  newInstance  com.ttv.demo.PlaceholderFragment  requireContext  com.ttv.demo.PlaceholderFragment  	ARG_TITLE *com.ttv.demo.PlaceholderFragment.Companion  Bundle *com.ttv.demo.PlaceholderFragment.Companion  PlaceholderFragment *com.ttv.demo.PlaceholderFragment.Companion  android *com.ttv.demo.PlaceholderFragment.Companion  apply *com.ttv.demo.PlaceholderFragment.Companion  newInstance *com.ttv.demo.PlaceholderFragment.Companion  view (com.ttv.demo.PlaceholderFragment.android  LayoutInflater -com.ttv.demo.PlaceholderFragment.android.view  	ViewGroup -com.ttv.demo.PlaceholderFragment.android.view  blue_primary com.ttv.demo.R.color  text_secondary com.ttv.demo.R.color  dimen_1 com.ttv.demo.R.dimen  dimen_48 com.ttv.demo.R.dimen  appBarLayout com.ttv.demo.R.id  clLayout com.ttv.demo.R.id  coIndicator com.ttv.demo.R.id  dynamic_bottom_tab com.ttv.demo.R.id  dynamic_bottom_tab_container com.ttv.demo.R.id  fluent_container com.ttv.demo.R.id  fragment_container com.ttv.demo.R.id  home_viewPager com.ttv.demo.R.id  main_tab com.ttv.demo.R.id  
tab_assets com.ttv.demo.R.id  tab_home com.ttv.demo.R.id  
tab_market com.ttv.demo.R.id  tab_profile com.ttv.demo.R.id  	tab_trade com.ttv.demo.R.id  
activity_main com.ttv.demo.R.layout  activity_main_with_navigation com.ttv.demo.R.layout  fragment_home_demo com.ttv.demo.R.layout  RoundLinearLayout com.ttv.demo.R.styleable  $RoundLinearLayout_rv_backgroundColor com.ttv.demo.R.styleable  !RoundLinearLayout_rv_cornerRadius com.ttv.demo.R.styleable  #RoundLinearLayout_rv_isRippleEnable com.ttv.demo.R.styleable  ARG_POSITION (com.ttv.demo.StickyBottomTabDemoActivity  	ARG_TITLE (com.ttv.demo.StickyBottomTabDemoActivity  AppBarLayout (com.ttv.demo.StickyBottomTabDemoActivity  Bundle (com.ttv.demo.StickyBottomTabDemoActivity  CoIndicator (com.ttv.demo.StickyBottomTabDemoActivity  CoordinatorLayout (com.ttv.demo.StickyBottomTabDemoActivity  DemoFragment (com.ttv.demo.StickyBottomTabDemoActivity  DynamicTabController (com.ttv.demo.StickyBottomTabDemoActivity  	Exception (com.ttv.demo.StickyBottomTabDemoActivity  Fragment (com.ttv.demo.StickyBottomTabDemoActivity  FragmentActivity (com.ttv.demo.StickyBottomTabDemoActivity  FragmentStateAdapter (com.ttv.demo.StickyBottomTabDemoActivity  Int (com.ttv.demo.StickyBottomTabDemoActivity  LayoutInflater (com.ttv.demo.StickyBottomTabDemoActivity  List (com.ttv.demo.StickyBottomTabDemoActivity  Log (com.ttv.demo.StickyBottomTabDemoActivity  NonInvasiveDynamicTabController (com.ttv.demo.StickyBottomTabDemoActivity  R (com.ttv.demo.StickyBottomTabDemoActivity  String (com.ttv.demo.StickyBottomTabDemoActivity  TabFragmentAdapter (com.ttv.demo.StickyBottomTabDemoActivity  TabLayoutMediator (com.ttv.demo.StickyBottomTabDemoActivity  TextView (com.ttv.demo.StickyBottomTabDemoActivity  View (com.ttv.demo.StickyBottomTabDemoActivity  	ViewGroup (com.ttv.demo.StickyBottomTabDemoActivity  
ViewPager2 (com.ttv.demo.StickyBottomTabDemoActivity  android (com.ttv.demo.StickyBottomTabDemoActivity  appBarLayout (com.ttv.demo.StickyBottomTabDemoActivity  coIndicator (com.ttv.demo.StickyBottomTabDemoActivity  coordinatorLayout (com.ttv.demo.StickyBottomTabDemoActivity  dynamicTabController (com.ttv.demo.StickyBottomTabDemoActivity  findViewById (com.ttv.demo.StickyBottomTabDemoActivity  getNavigationBarHeight (com.ttv.demo.StickyBottomTabDemoActivity  	getOrNull (com.ttv.demo.StickyBottomTabDemoActivity  indices (com.ttv.demo.StickyBottomTabDemoActivity  	initViews (com.ttv.demo.StickyBottomTabDemoActivity  listOf (com.ttv.demo.StickyBottomTabDemoActivity  newInstance (com.ttv.demo.StickyBottomTabDemoActivity  nonInvasiveController (com.ttv.demo.StickyBottomTabDemoActivity  	resources (com.ttv.demo.StickyBottomTabDemoActivity  setContentView (com.ttv.demo.StickyBottomTabDemoActivity  setupBottomNavigationPadding (com.ttv.demo.StickyBottomTabDemoActivity  setupBusinessListeners (com.ttv.demo.StickyBottomTabDemoActivity  setupDynamicTabController (com.ttv.demo.StickyBottomTabDemoActivity  setupViewPager (com.ttv.demo.StickyBottomTabDemoActivity  	tabTitles (com.ttv.demo.StickyBottomTabDemoActivity  	viewPager (com.ttv.demo.StickyBottomTabDemoActivity  ARG_POSITION 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  	ARG_TITLE 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  Bundle 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  	Companion 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  DemoFragment 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  Int 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  LayoutInflater 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  String 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  TextView 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  View 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  	ViewGroup 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  	arguments 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  newInstance 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  requireContext 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  ARG_POSITION ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  	ARG_TITLE ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  Bundle ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  DemoFragment ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  TextView ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  newInstance ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  DemoFragment ;com.ttv.demo.StickyBottomTabDemoActivity.TabFragmentAdapter  newInstance ;com.ttv.demo.StickyBottomTabDemoActivity.TabFragmentAdapter  	tabTitles ;com.ttv.demo.StickyBottomTabDemoActivity.TabFragmentAdapter  OnPageChangeCallback 3com.ttv.demo.StickyBottomTabDemoActivity.ViewPager2  content 0com.ttv.demo.StickyBottomTabDemoActivity.android  res 8com.ttv.demo.StickyBottomTabDemoActivity.android.content  
Configuration <com.ttv.demo.StickyBottomTabDemoActivity.android.content.res  OnPageChangeCallback com.ttv.demo.ViewPager2  content com.ttv.demo.android  view com.ttv.demo.android  widget com.ttv.demo.android  res com.ttv.demo.android.content  
Configuration  com.ttv.demo.android.content.res  LayoutInflater com.ttv.demo.android.view  	ViewGroup com.ttv.demo.android.view  	ImageView com.ttv.demo.android.widget  TextView com.ttv.demo.android.widget  
viewpager2 com.ttv.demo.androidx  adapter  com.ttv.demo.androidx.viewpager2  FragmentStateAdapter (com.ttv.demo.androidx.viewpager2.adapter  ANIMATION_DURATION com.ttv.demo.dynamic  AppBarLayout com.ttv.demo.dynamic  Boolean com.ttv.demo.dynamic  CoIndicator com.ttv.demo.dynamic  Context com.ttv.demo.dynamic  CoordinatorLayout com.ttv.demo.dynamic  DynamicTabController com.ttv.demo.dynamic  	Exception com.ttv.demo.dynamic  Gravity com.ttv.demo.dynamic  Int com.ttv.demo.dynamic  IntArray com.ttv.demo.dynamic  LinearLayout com.ttv.demo.dynamic  List com.ttv.demo.dynamic  Log com.ttv.demo.dynamic  NonInvasiveDynamicTabController com.ttv.demo.dynamic  R com.ttv.demo.dynamic  SCROLL_DIRECTION_DOWN com.ttv.demo.dynamic  SCROLL_DIRECTION_UP com.ttv.demo.dynamic  SCROLL_THRESHOLD com.ttv.demo.dynamic  SHOW_THRESHOLD com.ttv.demo.dynamic  String com.ttv.demo.dynamic  TAG com.ttv.demo.dynamic  	TabLayout com.ttv.demo.dynamic  TabLayoutMediator com.ttv.demo.dynamic  View com.ttv.demo.dynamic  	ViewGroup com.ttv.demo.dynamic  
ViewPager2 com.ttv.demo.dynamic  abs com.ttv.demo.dynamic  android com.ttv.demo.dynamic  androidx com.ttv.demo.dynamic  apply com.ttv.demo.dynamic  	getOrNull com.ttv.demo.dynamic  let com.ttv.demo.dynamic  
trimIndent com.ttv.demo.dynamic  OnOffsetChangedListener !com.ttv.demo.dynamic.AppBarLayout  ANIMATION_DURATION )com.ttv.demo.dynamic.DynamicTabController  AppBarLayout )com.ttv.demo.dynamic.DynamicTabController  Boolean )com.ttv.demo.dynamic.DynamicTabController  CoIndicator )com.ttv.demo.dynamic.DynamicTabController  Context )com.ttv.demo.dynamic.DynamicTabController  CoordinatorLayout )com.ttv.demo.dynamic.DynamicTabController  	Exception )com.ttv.demo.dynamic.DynamicTabController  Int )com.ttv.demo.dynamic.DynamicTabController  IntArray )com.ttv.demo.dynamic.DynamicTabController  LinearLayout )com.ttv.demo.dynamic.DynamicTabController  List )com.ttv.demo.dynamic.DynamicTabController  Log )com.ttv.demo.dynamic.DynamicTabController  R )com.ttv.demo.dynamic.DynamicTabController  SCROLL_THRESHOLD )com.ttv.demo.dynamic.DynamicTabController  String )com.ttv.demo.dynamic.DynamicTabController  TAG )com.ttv.demo.dynamic.DynamicTabController  TabLayoutMediator )com.ttv.demo.dynamic.DynamicTabController  View )com.ttv.demo.dynamic.DynamicTabController  
ViewPager2 )com.ttv.demo.dynamic.DynamicTabController  abs )com.ttv.demo.dynamic.DynamicTabController  android )com.ttv.demo.dynamic.DynamicTabController  androidx )com.ttv.demo.dynamic.DynamicTabController  appBarLayout )com.ttv.demo.dynamic.DynamicTabController  appBarOffsetChangeListener )com.ttv.demo.dynamic.DynamicTabController  apply )com.ttv.demo.dynamic.DynamicTabController  bottomNavigationHeight )com.ttv.demo.dynamic.DynamicTabController  	bottomTab )com.ttv.demo.dynamic.DynamicTabController  bottomTabContainer )com.ttv.demo.dynamic.DynamicTabController  context )com.ttv.demo.dynamic.DynamicTabController  coordinatorLayout )com.ttv.demo.dynamic.DynamicTabController  createBottomTabContainer )com.ttv.demo.dynamic.DynamicTabController  debugStatus )com.ttv.demo.dynamic.DynamicTabController  destroy )com.ttv.demo.dynamic.DynamicTabController  detectBottomNavigationHeight )com.ttv.demo.dynamic.DynamicTabController  detectMainAppNavigationHeight )com.ttv.demo.dynamic.DynamicTabController  	getOrNull )com.ttv.demo.dynamic.DynamicTabController  
getStatusInfo )com.ttv.demo.dynamic.DynamicTabController  handleTabVisibilityLogic )com.ttv.demo.dynamic.DynamicTabController  hideBottomTabWithAnimation )com.ttv.demo.dynamic.DynamicTabController  
initialize )com.ttv.demo.dynamic.DynamicTabController  isAnimating )com.ttv.demo.dynamic.DynamicTabController  isBottomTabVisible )com.ttv.demo.dynamic.DynamicTabController  isDestroyed )com.ttv.demo.dynamic.DynamicTabController  
isInitialized )com.ttv.demo.dynamic.DynamicTabController  isOriginalTabInViewport )com.ttv.demo.dynamic.DynamicTabController  lastScrollY )com.ttv.demo.dynamic.DynamicTabController  let )com.ttv.demo.dynamic.DynamicTabController  onConfigurationChanged )com.ttv.demo.dynamic.DynamicTabController  onLifecycleChanged )com.ttv.demo.dynamic.DynamicTabController  
onPageChanged )com.ttv.demo.dynamic.DynamicTabController  originalTab )com.ttv.demo.dynamic.DynamicTabController  "reconfigureNavigationBarAdaptation )com.ttv.demo.dynamic.DynamicTabController  scrollDirection )com.ttv.demo.dynamic.DynamicTabController  setDebugMode )com.ttv.demo.dynamic.DynamicTabController  setupScrollListener )com.ttv.demo.dynamic.DynamicTabController  setupTabSync )com.ttv.demo.dynamic.DynamicTabController  showBottomTabWithAnimation )com.ttv.demo.dynamic.DynamicTabController  tabLayoutMediator )com.ttv.demo.dynamic.DynamicTabController  	tabTitles )com.ttv.demo.dynamic.DynamicTabController  
trimIndent )com.ttv.demo.dynamic.DynamicTabController  	viewPager )com.ttv.demo.dynamic.DynamicTabController  OnOffsetChangedListener 6com.ttv.demo.dynamic.DynamicTabController.AppBarLayout  ANIMATION_DURATION 3com.ttv.demo.dynamic.DynamicTabController.Companion  AppBarLayout 3com.ttv.demo.dynamic.DynamicTabController.Companion  IntArray 3com.ttv.demo.dynamic.DynamicTabController.Companion  Log 3com.ttv.demo.dynamic.DynamicTabController.Companion  R 3com.ttv.demo.dynamic.DynamicTabController.Companion  SCROLL_THRESHOLD 3com.ttv.demo.dynamic.DynamicTabController.Companion  TAG 3com.ttv.demo.dynamic.DynamicTabController.Companion  TabLayoutMediator 3com.ttv.demo.dynamic.DynamicTabController.Companion  View 3com.ttv.demo.dynamic.DynamicTabController.Companion  abs 3com.ttv.demo.dynamic.DynamicTabController.Companion  apply 3com.ttv.demo.dynamic.DynamicTabController.Companion  	getOrNull 3com.ttv.demo.dynamic.DynamicTabController.Companion  let 3com.ttv.demo.dynamic.DynamicTabController.Companion  
trimIndent 3com.ttv.demo.dynamic.DynamicTabController.Companion  app 1com.ttv.demo.dynamic.DynamicTabController.android  Activity 5com.ttv.demo.dynamic.DynamicTabController.android.app  fragment 2com.ttv.demo.dynamic.DynamicTabController.androidx  app ;com.ttv.demo.dynamic.DynamicTabController.androidx.fragment  Fragment ?com.ttv.demo.dynamic.DynamicTabController.androidx.fragment.app  ANIMATION_DURATION 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  AppBarLayout 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  Boolean 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  CoIndicator 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  Context 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  CoordinatorLayout 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  	Exception 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  Gravity 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  Int 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  IntArray 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  LinearLayout 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  List 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  Log 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  R 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  SCROLL_DIRECTION_DOWN 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  SCROLL_DIRECTION_UP 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  SHOW_THRESHOLD 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  String 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  TAG 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  	TabLayout 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  TabLayoutMediator 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  View 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  	ViewGroup 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  
ViewPager2 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  abs 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  android 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  appBarLayout 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  apply 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  context 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  coordinatorLayout 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  createDynamicTabContainer 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  detectScrollDirection 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  disable 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  
dynamicTab 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  dynamicTabContainer 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  	getOrNull 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  handleTabVisibility 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  
hideBottomTab 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  isAnimating 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  isBottomTabVisible 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  	isEnabled 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  isOriginalTabInViewport 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  lastVerticalOffset 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  let 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  originalTab 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  scrollDirection 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  scrollListener 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  setupScrollListener 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  setupTabSync 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  
showBottomTab 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  tabLayoutMediator 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  	tabTitles 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  	viewPager 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  OnOffsetChangedListener Acom.ttv.demo.dynamic.NonInvasiveDynamicTabController.AppBarLayout  ANIMATION_DURATION >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  AppBarLayout >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  CoordinatorLayout >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  Gravity >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  IntArray >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  LinearLayout >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  Log >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  R >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  SCROLL_DIRECTION_DOWN >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  SCROLL_DIRECTION_UP >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  SHOW_THRESHOLD >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  TAG >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  	TabLayout >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  TabLayoutMediator >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  View >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  abs >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  android >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  apply >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  	getOrNull >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  let >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  app com.ttv.demo.dynamic.android  Activity  com.ttv.demo.dynamic.android.app  fragment com.ttv.demo.dynamic.androidx  app &com.ttv.demo.dynamic.androidx.fragment  Fragment *com.ttv.demo.dynamic.androidx.fragment.app  	Exception 	java.lang  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  Nothing kotlin  apply kotlin  let kotlin  not kotlin.Boolean  	compareTo kotlin.Float  div kotlin.Float  plus kotlin.Float  toInt kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  
unaryMinus 
kotlin.Int  get kotlin.IntArray  toInt kotlin.Long  plus 
kotlin.String  
trimIndent 
kotlin.String  IntIterator kotlin.collections  List kotlin.collections  	getOrNull kotlin.collections  indices kotlin.collections  listOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  get kotlin.collections.List  	getOrNull kotlin.collections.List  indices kotlin.collections.List  size kotlin.collections.List  JvmOverloads 
kotlin.jvm  abs kotlin.math  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  	getOrNull kotlin.text  indices kotlin.text  
trimIndent kotlin.text  isClickable android.view.View  isFocusable android.view.View  bottomMargin )android.view.ViewGroup.MarginLayoutParams  bottomNavigationHeight android.widget.LinearLayout  	bottomTab android.widget.LinearLayout  isClickable android.widget.LinearLayout  isFocusable android.widget.LinearLayout  layoutParams android.widget.LinearLayout  android @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  behavior @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  bottomMargin @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  bottomNavigationHeight @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  apply 1com.hbg.module.libkt.custom.indicator.CoIndicator  isClickable 1com.hbg.module.libkt.custom.indicator.CoIndicator  isFocusable 1com.hbg.module.libkt.custom.indicator.CoIndicator  bottomNavigationHeight com.ttv.demo.dynamic  	bottomTab com.ttv.demo.dynamic  LayoutParams &com.ttv.demo.dynamic.CoordinatorLayout  setupBottomTabLayoutParams )com.ttv.demo.dynamic.DynamicTabController  setupTouchEventHandling )com.ttv.demo.dynamic.DynamicTabController  CoordinatorLayout 3com.ttv.demo.dynamic.DynamicTabController.Companion  android 3com.ttv.demo.dynamic.DynamicTabController.Companion  bottomNavigationHeight 3com.ttv.demo.dynamic.DynamicTabController.Companion  	bottomTab 3com.ttv.demo.dynamic.DynamicTabController.Companion  LayoutParams ;com.ttv.demo.dynamic.DynamicTabController.CoordinatorLayout  OnTouchListener android.view.View  measure android.view.View  measuredHeight android.view.View  postDelayed android.view.View  setOnTouchListener android.view.View  EXACTLY android.view.View.MeasureSpec  UNSPECIFIED android.view.View.MeasureSpec  makeMeasureSpec android.view.View.MeasureSpec  <SAM-CONSTRUCTOR> !android.view.View.OnTouchListener  measure android.widget.LinearLayout  measuredHeight android.widget.LinearLayout  setOnTouchListener android.widget.LinearLayout  width android.widget.LinearLayout  apply %androidx.core.widget.NestedScrollView  isFillViewport %androidx.core.widget.NestedScrollView  isNestedScrollingEnabled %androidx.core.widget.NestedScrollView  
visibility %androidx.core.widget.NestedScrollView  apply %androidx.viewpager2.widget.ViewPager2  isUserInputEnabled %androidx.viewpager2.widget.ViewPager2  offscreenPageLimit %androidx.viewpager2.widget.ViewPager2  fluent_content_nsv com.ttv.demo.R.id  com com.ttv.demo.dynamic  com )com.ttv.demo.dynamic.DynamicTabController  diagnoseTouchConflicts )com.ttv.demo.dynamic.DynamicTabController  ensureProperTouchHandling )com.ttv.demo.dynamic.DynamicTabController  ensureScrollableViewsEnabled )com.ttv.demo.dynamic.DynamicTabController  com 3com.ttv.demo.dynamic.DynamicTabController.Companion  core 2com.ttv.demo.dynamic.DynamicTabController.androidx  widget 7com.ttv.demo.dynamic.DynamicTabController.androidx.core  NestedScrollView >com.ttv.demo.dynamic.DynamicTabController.androidx.core.widget  core com.ttv.demo.dynamic.androidx  widget "com.ttv.demo.dynamic.androidx.core  NestedScrollView )com.ttv.demo.dynamic.androidx.core.widget  detectNavigationHeight )com.ttv.demo.dynamic.DynamicTabController  
hideBottomTab )com.ttv.demo.dynamic.DynamicTabController  scrollListener )com.ttv.demo.dynamic.DynamicTabController  setupBottomTabContainer )com.ttv.demo.dynamic.DynamicTabController  
showBottomTab )com.ttv.demo.dynamic.DynamicTabController  isNestedScrollingEnabled android.view.View  isScrollbarFadingEnabled android.view.View  	minHeight android.widget.TextView  isScrollbarFadingEnabled %androidx.core.widget.NestedScrollView  apply /com.google.android.material.appbar.AppBarLayout  isNestedScrollingEnabled /com.google.android.material.appbar.AppBarLayout  kotlin com.ttv.demo.dynamic  checkTabRealTimePosition )com.ttv.demo.dynamic.DynamicTabController  ensureScrollingWorks )com.ttv.demo.dynamic.DynamicTabController  isOriginalTabVisibleByOffset )com.ttv.demo.dynamic.DynamicTabController  kotlin )com.ttv.demo.dynamic.DynamicTabController  kotlin 3com.ttv.demo.dynamic.DynamicTabController.Companion  times kotlin.Float  kotlin 
kotlin.jvm  KClass kotlin.reflect  androidx android.app.Activity  apply android.app.Activity  LinearLayout #android.app.Activity.android.widget  androidx android.content.Context  apply android.content.Context  LinearLayout &android.content.Context.android.widget  androidx android.content.ContextWrapper  apply android.content.ContextWrapper  LinearLayout -android.content.ContextWrapper.android.widget  
parseColor android.graphics.Color  MotionEvent android.view  androidx  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  LinearLayout /android.view.ContextThemeWrapper.android.widget  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  action android.view.MotionEvent  x android.view.MotionEvent  y android.view.MotionEvent  MotionEvent android.view.View  RecyclerView android.view.View  TAG android.view.View  
ViewCompat android.view.View  canScrollVertically android.view.View  fitsSystemWindows android.view.View  kotlin android.view.View  
minimumHeight android.view.View  overScrollMode android.view.View  
plusAssign android.view.View  MotionEvent android.view.ViewGroup  RecyclerView android.view.ViewGroup  TAG android.view.ViewGroup  
ViewCompat android.view.ViewGroup  
childCount android.view.ViewGroup  kotlin android.view.ViewGroup  
plusAssign android.view.ViewGroup  removeAllViews android.view.ViewGroup  
setMargins )android.view.ViewGroup.MarginLayoutParams  MotionEvent android.widget.FrameLayout  RecyclerView android.widget.FrameLayout  TAG android.widget.FrameLayout  
ViewCompat android.widget.FrameLayout  kotlin android.widget.FrameLayout  
plusAssign android.widget.FrameLayout  
childCount android.widget.LinearLayout  removeAllViews android.widget.LinearLayout  WRAP_CONTENT (android.widget.LinearLayout.LayoutParams  apply (android.widget.LinearLayout.LayoutParams  
setMargins (android.widget.LinearLayout.LayoutParams  androidx #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  LinearLayout 2androidx.activity.ComponentActivity.android.widget  androidx (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  LinearLayout 7androidx.appcompat.app.AppCompatActivity.android.widget  CardView androidx.cardview.widget  addView !androidx.cardview.widget.CardView  android !androidx.cardview.widget.CardView  apply !androidx.cardview.widget.CardView  
cardElevation !androidx.cardview.widget.CardView  layoutParams !androidx.cardview.widget.CardView  radius !androidx.cardview.widget.CardView  Log 3androidx.coordinatorlayout.widget.CoordinatorLayout  TAG 3androidx.coordinatorlayout.widget.CoordinatorLayout  apply 3androidx.coordinatorlayout.widget.CoordinatorLayout  
childCount 3androidx.coordinatorlayout.widget.CoordinatorLayout  fitsSystemWindows 3androidx.coordinatorlayout.widget.CoordinatorLayout  
getChildAt 3androidx.coordinatorlayout.widget.CoordinatorLayout  isNestedScrollingEnabled 3androidx.coordinatorlayout.widget.CoordinatorLayout  	javaClass 3androidx.coordinatorlayout.widget.CoordinatorLayout  androidx #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  LinearLayout 2androidx.core.app.ComponentActivity.android.widget  
ViewCompat androidx.core.view  
TYPE_TOUCH androidx.core.view.ViewCompat  MotionEvent %androidx.core.widget.NestedScrollView  RecyclerView %androidx.core.widget.NestedScrollView  TAG %androidx.core.widget.NestedScrollView  
ViewCompat %androidx.core.widget.NestedScrollView  
computeScroll %androidx.core.widget.NestedScrollView  kotlin %androidx.core.widget.NestedScrollView  onInterceptTouchEvent %androidx.core.widget.NestedScrollView  onStartNestedScroll %androidx.core.widget.NestedScrollView  onTouchEvent %androidx.core.widget.NestedScrollView  
plusAssign %androidx.core.widget.NestedScrollView  smoothScrollTo %androidx.core.widget.NestedScrollView  startNestedScroll %androidx.core.widget.NestedScrollView  stopNestedScroll %androidx.core.widget.NestedScrollView  androidx androidx.fragment.app.Fragment  widget &androidx.fragment.app.Fragment.android  LinearLayout -androidx.fragment.app.Fragment.android.widget  androidx &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  LinearLayout 5androidx.fragment.app.FragmentActivity.android.widget  RecyclerView androidx.recyclerview.widget  SCROLL_STATE_DRAGGING )androidx.recyclerview.widget.RecyclerView  SCROLL_STATE_IDLE )androidx.recyclerview.widget.RecyclerView  Log /com.google.android.material.appbar.AppBarLayout  TAG /com.google.android.material.appbar.AppBarLayout  
childCount /com.google.android.material.appbar.AppBarLayout  	elevation /com.google.android.material.appbar.AppBarLayout  
getChildAt /com.google.android.material.appbar.AppBarLayout  height /com.google.android.material.appbar.AppBarLayout  
minimumHeight /com.google.android.material.appbar.AppBarLayout  post /com.google.android.material.appbar.AppBarLayout  post 1com.hbg.module.libkt.custom.indicator.CoIndicator  Boolean com.huobi.view  MotionEvent com.huobi.view  OnScrollChangedListener com.huobi.view  RecyclerView com.huobi.view  ScrollStateListener com.huobi.view  TAG com.huobi.view  View com.huobi.view  
ViewCompat com.huobi.view  kotlin com.huobi.view  
plusAssign com.huobi.view  AttributeSet !com.huobi.view.MyNestedScrollView  Boolean !com.huobi.view.MyNestedScrollView  Context !com.huobi.view.MyNestedScrollView  Int !com.huobi.view.MyNestedScrollView  JvmOverloads !com.huobi.view.MyNestedScrollView  Log !com.huobi.view.MyNestedScrollView  MotionEvent !com.huobi.view.MyNestedScrollView  NestedScrollView !com.huobi.view.MyNestedScrollView  OnScrollChangedListener !com.huobi.view.MyNestedScrollView  RecyclerView !com.huobi.view.MyNestedScrollView  ScrollStateListener !com.huobi.view.MyNestedScrollView  TAG !com.huobi.view.MyNestedScrollView  View !com.huobi.view.MyNestedScrollView  
ViewCompat !com.huobi.view.MyNestedScrollView  apply !com.huobi.view.MyNestedScrollView  canScrollVertically !com.huobi.view.MyNestedScrollView  
computeScroll !com.huobi.view.MyNestedScrollView  dispatchScrollState !com.huobi.view.MyNestedScrollView  height !com.huobi.view.MyNestedScrollView  isFillViewport !com.huobi.view.MyNestedScrollView  isNestedScrollingEnabled !com.huobi.view.MyNestedScrollView  isScrollbarFadingEnabled !com.huobi.view.MyNestedScrollView  kotlin !com.huobi.view.MyNestedScrollView  lastX !com.huobi.view.MyNestedScrollView  lastY !com.huobi.view.MyNestedScrollView  mIsStartScroll !com.huobi.view.MyNestedScrollView  mOnScrollChangedListener !com.huobi.view.MyNestedScrollView  mScrollListener !com.huobi.view.MyNestedScrollView  mState !com.huobi.view.MyNestedScrollView  
mTouchType !com.huobi.view.MyNestedScrollView  overScrollMode !com.huobi.view.MyNestedScrollView  
plusAssign !com.huobi.view.MyNestedScrollView  post !com.huobi.view.MyNestedScrollView  postDelayed !com.huobi.view.MyNestedScrollView  
scrollable !com.huobi.view.MyNestedScrollView  setOnScrollChangedListener !com.huobi.view.MyNestedScrollView  setScrollingEnabled !com.huobi.view.MyNestedScrollView  smoothScrollTo !com.huobi.view.MyNestedScrollView  	xDistance !com.huobi.view.MyNestedScrollView  	yDistance !com.huobi.view.MyNestedScrollView  MotionEvent +com.huobi.view.MyNestedScrollView.Companion  RecyclerView +com.huobi.view.MyNestedScrollView.Companion  TAG +com.huobi.view.MyNestedScrollView.Companion  
ViewCompat +com.huobi.view.MyNestedScrollView.Companion  android +com.huobi.view.MyNestedScrollView.Companion  kotlin +com.huobi.view.MyNestedScrollView.Companion  
plusAssign +com.huobi.view.MyNestedScrollView.Companion  onScrollChange 9com.huobi.view.MyNestedScrollView.OnScrollChangedListener  onStartNestedScroll 5com.huobi.view.MyNestedScrollView.ScrollStateListener  onStateChanged 5com.huobi.view.MyNestedScrollView.ScrollStateListener  onStopNestedScroll 5com.huobi.view.MyNestedScrollView.ScrollStateListener  addTestContentToFluentContainer com.ttv.demo.HomeDemoFragment  androidx com.ttv.demo.HomeDemoFragment  android 'com.ttv.demo.HomeDemoFragment.Companion  androidx 'com.ttv.demo.HomeDemoFragment.Companion  widget %com.ttv.demo.HomeDemoFragment.android  LinearLayout ,com.ttv.demo.HomeDemoFragment.android.widget  addTestContentToFluentContainer (com.ttv.demo.StickyBottomTabDemoActivity  androidx (com.ttv.demo.StickyBottomTabDemoActivity  apply (com.ttv.demo.StickyBottomTabDemoActivity  widget 0com.ttv.demo.StickyBottomTabDemoActivity.android  LinearLayout 7com.ttv.demo.StickyBottomTabDemoActivity.android.widget  LinearLayout com.ttv.demo.android.widget  ScrollDiagnosticHelper com.ttv.demo.dynamic  ScrollFixVerifier com.ttv.demo.dynamic  handleNestedScrollViewScroll com.ttv.demo.dynamic  isAnimating com.ttv.demo.dynamic  	isEnabled com.ttv.demo.dynamic  	javaClass com.ttv.demo.dynamic  until com.ttv.demo.dynamic  ScrollDiagnosticHelper )com.ttv.demo.dynamic.DynamicTabController  ScrollFixVerifier )com.ttv.demo.dynamic.DynamicTabController  checkContentHeight )com.ttv.demo.dynamic.DynamicTabController  checkTabVisibilityByPosition )com.ttv.demo.dynamic.DynamicTabController  handleNestedScrollViewScroll )com.ttv.demo.dynamic.DynamicTabController  performScrollDiagnosis )com.ttv.demo.dynamic.DynamicTabController  setupAppBarLayoutListener )com.ttv.demo.dynamic.DynamicTabController  setupNestedScrollViewListener )com.ttv.demo.dynamic.DynamicTabController  ScrollDiagnosticHelper 3com.ttv.demo.dynamic.DynamicTabController.Companion  ScrollFixVerifier 3com.ttv.demo.dynamic.DynamicTabController.Companion  handleNestedScrollViewScroll 3com.ttv.demo.dynamic.DynamicTabController.Companion  widget 1com.ttv.demo.dynamic.DynamicTabController.android  LinearLayout 8com.ttv.demo.dynamic.DynamicTabController.android.widget  huobi -com.ttv.demo.dynamic.DynamicTabController.com  view 3com.ttv.demo.dynamic.DynamicTabController.com.huobi  MyNestedScrollView 8com.ttv.demo.dynamic.DynamicTabController.com.huobi.view  OnScrollChangedListener Kcom.ttv.demo.dynamic.DynamicTabController.com.huobi.view.MyNestedScrollView  androidx 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  com 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  getScrollDirectionName 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  handleNestedScrollViewScroll 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  handleTabVisibilityByScroll 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  setupAppBarLayoutListener 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  setupNestedScrollViewListener 4com.ttv.demo.dynamic.NonInvasiveDynamicTabController  handleNestedScrollViewScroll >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  isAnimating >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  	isEnabled >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.Companion  core =com.ttv.demo.dynamic.NonInvasiveDynamicTabController.androidx  widget Bcom.ttv.demo.dynamic.NonInvasiveDynamicTabController.androidx.core  NestedScrollView Icom.ttv.demo.dynamic.NonInvasiveDynamicTabController.androidx.core.widget  huobi 8com.ttv.demo.dynamic.NonInvasiveDynamicTabController.com  view >com.ttv.demo.dynamic.NonInvasiveDynamicTabController.com.huobi  MyNestedScrollView Ccom.ttv.demo.dynamic.NonInvasiveDynamicTabController.com.huobi.view  OnScrollChangedListener Vcom.ttv.demo.dynamic.NonInvasiveDynamicTabController.com.huobi.view.MyNestedScrollView  AppBarLayout +com.ttv.demo.dynamic.ScrollDiagnosticHelper  CoIndicator +com.ttv.demo.dynamic.ScrollDiagnosticHelper  Context +com.ttv.demo.dynamic.ScrollDiagnosticHelper  CoordinatorLayout +com.ttv.demo.dynamic.ScrollDiagnosticHelper  IntArray +com.ttv.demo.dynamic.ScrollDiagnosticHelper  Log +com.ttv.demo.dynamic.ScrollDiagnosticHelper  R +com.ttv.demo.dynamic.ScrollDiagnosticHelper  TAG +com.ttv.demo.dynamic.ScrollDiagnosticHelper  android +com.ttv.demo.dynamic.ScrollDiagnosticHelper  appBarLayout +com.ttv.demo.dynamic.ScrollDiagnosticHelper  apply +com.ttv.demo.dynamic.ScrollDiagnosticHelper  applyCommonFixes +com.ttv.demo.dynamic.ScrollDiagnosticHelper  checkAppBarLayout +com.ttv.demo.dynamic.ScrollDiagnosticHelper  checkContentHeight +com.ttv.demo.dynamic.ScrollDiagnosticHelper  checkLayoutHierarchy +com.ttv.demo.dynamic.ScrollDiagnosticHelper  checkNestedScrollView +com.ttv.demo.dynamic.ScrollDiagnosticHelper  checkScrollConfiguration +com.ttv.demo.dynamic.ScrollDiagnosticHelper  checkTabPosition +com.ttv.demo.dynamic.ScrollDiagnosticHelper  com +com.ttv.demo.dynamic.ScrollDiagnosticHelper  context +com.ttv.demo.dynamic.ScrollDiagnosticHelper  coordinatorLayout +com.ttv.demo.dynamic.ScrollDiagnosticHelper  	javaClass +com.ttv.demo.dynamic.ScrollDiagnosticHelper  originalTab +com.ttv.demo.dynamic.ScrollDiagnosticHelper  performFullDiagnosis +com.ttv.demo.dynamic.ScrollDiagnosticHelper  until +com.ttv.demo.dynamic.ScrollDiagnosticHelper  IntArray 5com.ttv.demo.dynamic.ScrollDiagnosticHelper.Companion  Log 5com.ttv.demo.dynamic.ScrollDiagnosticHelper.Companion  R 5com.ttv.demo.dynamic.ScrollDiagnosticHelper.Companion  TAG 5com.ttv.demo.dynamic.ScrollDiagnosticHelper.Companion  apply 5com.ttv.demo.dynamic.ScrollDiagnosticHelper.Companion  	javaClass 5com.ttv.demo.dynamic.ScrollDiagnosticHelper.Companion  until 5com.ttv.demo.dynamic.ScrollDiagnosticHelper.Companion  widget 3com.ttv.demo.dynamic.ScrollDiagnosticHelper.android  LinearLayout :com.ttv.demo.dynamic.ScrollDiagnosticHelper.android.widget  huobi /com.ttv.demo.dynamic.ScrollDiagnosticHelper.com  view 5com.ttv.demo.dynamic.ScrollDiagnosticHelper.com.huobi  MyNestedScrollView :com.ttv.demo.dynamic.ScrollDiagnosticHelper.com.huobi.view  AppBarLayout &com.ttv.demo.dynamic.ScrollFixVerifier  Boolean &com.ttv.demo.dynamic.ScrollFixVerifier  CoIndicator &com.ttv.demo.dynamic.ScrollFixVerifier  Context &com.ttv.demo.dynamic.ScrollFixVerifier  CoordinatorLayout &com.ttv.demo.dynamic.ScrollFixVerifier  	Exception &com.ttv.demo.dynamic.ScrollFixVerifier  Int &com.ttv.demo.dynamic.ScrollFixVerifier  IntArray &com.ttv.demo.dynamic.ScrollFixVerifier  Log &com.ttv.demo.dynamic.ScrollFixVerifier  R &com.ttv.demo.dynamic.ScrollFixVerifier  TAG &com.ttv.demo.dynamic.ScrollFixVerifier  android &com.ttv.demo.dynamic.ScrollFixVerifier  androidx &com.ttv.demo.dynamic.ScrollFixVerifier  appBarLayout &com.ttv.demo.dynamic.ScrollFixVerifier  com &com.ttv.demo.dynamic.ScrollFixVerifier  context &com.ttv.demo.dynamic.ScrollFixVerifier  coordinatorLayout &com.ttv.demo.dynamic.ScrollFixVerifier  originalTab &com.ttv.demo.dynamic.ScrollFixVerifier  testContentHeight &com.ttv.demo.dynamic.ScrollFixVerifier  testNestedScrollViewExists &com.ttv.demo.dynamic.ScrollFixVerifier  testScrollConfiguration &com.ttv.demo.dynamic.ScrollFixVerifier  testTabPositionDetection &com.ttv.demo.dynamic.ScrollFixVerifier  	verifyFix &com.ttv.demo.dynamic.ScrollFixVerifier  IntArray 0com.ttv.demo.dynamic.ScrollFixVerifier.Companion  Log 0com.ttv.demo.dynamic.ScrollFixVerifier.Companion  R 0com.ttv.demo.dynamic.ScrollFixVerifier.Companion  TAG 0com.ttv.demo.dynamic.ScrollFixVerifier.Companion  widget .com.ttv.demo.dynamic.ScrollFixVerifier.android  LinearLayout 5com.ttv.demo.dynamic.ScrollFixVerifier.android.widget  core /com.ttv.demo.dynamic.ScrollFixVerifier.androidx  widget 4com.ttv.demo.dynamic.ScrollFixVerifier.androidx.core  NestedScrollView ;com.ttv.demo.dynamic.ScrollFixVerifier.androidx.core.widget  huobi *com.ttv.demo.dynamic.ScrollFixVerifier.com  view 0com.ttv.demo.dynamic.ScrollFixVerifier.com.huobi  MyNestedScrollView 5com.ttv.demo.dynamic.ScrollFixVerifier.com.huobi.view  OnScrollChangedListener Hcom.ttv.demo.dynamic.ScrollFixVerifier.com.huobi.view.MyNestedScrollView  widget com.ttv.demo.dynamic.android  LinearLayout #com.ttv.demo.dynamic.android.widget  huobi com.ttv.demo.dynamic.com  view com.ttv.demo.dynamic.com.huobi  MyNestedScrollView #com.ttv.demo.dynamic.com.huobi.view  OnScrollChangedListener 6com.ttv.demo.dynamic.com.huobi.view.MyNestedScrollView  Class 	java.lang  
simpleName java.lang.Class  minus kotlin.Float  
plusAssign kotlin.Float  
plusAssign kotlin.collections  	javaClass 
kotlin.jvm  	CharRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  	javaClass !com.huobi.view.MyNestedScrollView  Float com.ttv.demo.dynamic  Float )com.ttv.demo.dynamic.DynamicTabController  handleAppBarLayoutScroll )com.ttv.demo.dynamic.DynamicTabController  	javaClass )com.ttv.demo.dynamic.DynamicTabController  	javaClass 3com.ttv.demo.dynamic.DynamicTabController.Companion  detectScrollDirection )com.ttv.demo.dynamic.DynamicTabController  lastVerticalOffset )com.ttv.demo.dynamic.DynamicTabController  rem kotlin.Float  div 
kotlin.Int                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  