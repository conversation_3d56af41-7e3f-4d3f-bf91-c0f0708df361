1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ttv.demo"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.ttv.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.ttv.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:5:5-33:19
18        android:allowBackup="true"
18-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.Demo" >
29-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:13:9-42
30
31        <!-- 原始演示Activity -->
32        <activity
32-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:17:9-19:40
33            android:name="com.ttv.demo.StickyBottomTabDemoActivity"
33-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:18:13-56
34            android:exported="false" />
34-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:19:13-37
35
36        <!-- 新的主Activity - 模拟客户环境 -->
37        <activity
37-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:22:9-29:20
38            android:name="com.ttv.demo.MainActivityWithNavigation"
38-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:23:13-55
39            android:exported="true" >
39-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:24:13-36
40            <intent-filter>
40-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:25:13-28:29
41                <action android:name="android.intent.action.MAIN" />
41-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:26:17-69
41-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:26:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:27:17-77
43-->D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:27:27-74
44            </intent-filter>
45        </activity>
46
47        <provider
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
48            android:name="androidx.startup.InitializationProvider"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
49            android:authorities="com.ttv.demo.androidx-startup"
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
50            android:exported="false" >
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
51            <meta-data
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.emoji2.text.EmojiCompatInitializer"
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
53                android:value="androidx.startup" />
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
54            <meta-data
54-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
55-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
56                android:value="androidx.startup" />
56-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
57            <meta-data
57-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
58-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
59                android:value="androidx.startup" />
59-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
60        </provider>
61
62        <receiver
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
63            android:name="androidx.profileinstaller.ProfileInstallReceiver"
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
64            android:directBootAware="false"
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
65            android:enabled="true"
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
66            android:exported="true"
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
67            android:permission="android.permission.DUMP" >
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
68            <intent-filter>
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
69                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
70            </intent-filter>
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
72                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
75                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
78                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
79            </intent-filter>
80        </receiver>
81    </application>
82
83</manifest>
