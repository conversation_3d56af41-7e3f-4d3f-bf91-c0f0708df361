package com.huobi.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.RecyclerView

/**
 * 自定义NestedScrollView - 修复版本
 *
 * 修复内容：
 * 1. 统一Java和Kotlin版本的功能
 * 2. 修复滚动状态管理
 * 3. 增强滚动监听功能
 * 4. 修复触摸事件处理
 */
class MyNestedScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : NestedScrollView(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "MyNestedScrollView"
    }

    // 滚动状态管理
    private var mState = RecyclerView.SCROLL_STATE_IDLE
    private var mTouchType = ViewCompat.TYPE_TOUCH
    private var mIsStartScroll = false
    private var scrollable = true

    // 触摸事件处理
    private var xDistance = 0f
    private var yDistance = 0f
    private var lastX = 0f
    private var lastY = 0f

    // 监听器
    private var mScrollListener: ScrollStateListener? = null
    private var mOnScrollChangedListener: OnScrollChangedListener? = null

    /**
     * 滚动状态监听器
     */
    interface ScrollStateListener {
        fun onStateChanged(state: Int)
        fun onStopNestedScroll()
        fun onStartNestedScroll()
    }

    /**
     * 滚动变化监听器
     */
    interface OnScrollChangedListener {
        fun onScrollChange(
            v: NestedScrollView,
            scrollX: Int,
            scrollY: Int,
            oldScrollX: Int,
            oldScrollY: Int
        )
    }

    /**
     * 设置滚动状态监听器
     */
    fun setScrollStateListener(scrollListener: ScrollStateListener?) {
        this.mScrollListener = scrollListener
    }

    /**
     * 设置滚动变化监听器
     */
    fun setOnScrollChangedListener(listener: OnScrollChangedListener?) {
        this.mOnScrollChangedListener = listener
    }

    /**
     * 设置是否可滚动
     */
    fun setScrollingEnabled(enabled: Boolean) {
        scrollable = enabled
        android.util.Log.d(TAG, "设置滚动状态: $enabled")
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        if (!scrollable) {
            return false
        }

        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                xDistance = 0f
                yDistance = 0f
                lastX = ev.x
                lastY = ev.y
                computeScroll()
            }
            MotionEvent.ACTION_MOVE -> {
                val curX = ev.x
                val curY = ev.y
                xDistance += kotlin.math.abs(curX - lastX)
                yDistance += kotlin.math.abs(curY - lastY)
                lastX = curX
                lastY = curY

                // 如果横向滑动距离大于纵向，不拦截事件（让ViewPager处理）
                if (xDistance > yDistance) {
                    return false
                }
            }
        }

        return super.onInterceptTouchEvent(ev)
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        return scrollable && super.onTouchEvent(ev)
    }

    override fun onScrollChanged(scrollX: Int, scrollY: Int, oldScrollX: Int, oldScrollY: Int) {
        super.onScrollChanged(scrollX, scrollY, oldScrollX, oldScrollY)

        // 调试日志
        android.util.Log.d(TAG, "📱 onScrollChanged: scrollY=$scrollY, oldScrollY=$oldScrollY, delta=${scrollY - oldScrollY}")

        // 通知监听器
        mOnScrollChangedListener?.onScrollChange(this, scrollX, scrollY, oldScrollX, oldScrollY)
    }

    override fun startNestedScroll(axes: Int, type: Int): Boolean {
        mTouchType = type
        if (mScrollListener != null && !mIsStartScroll) {
            mIsStartScroll = true
            mScrollListener?.onStartNestedScroll()
        }
        return super.startNestedScroll(axes, type)
    }

    override fun stopNestedScroll(type: Int) {
        super.stopNestedScroll(type)
        dispatchScrollState(RecyclerView.SCROLL_STATE_IDLE)
        if (mScrollListener != null && mTouchType == type && mIsStartScroll) {
            mIsStartScroll = false
            mScrollListener?.onStopNestedScroll()
        }
    }

    override fun onStartNestedScroll(child: View, target: View, nestedScrollAxes: Int): Boolean {
        dispatchScrollState(RecyclerView.SCROLL_STATE_DRAGGING)
        return super.onStartNestedScroll(child, target, nestedScrollAxes)
    }

    override fun startNestedScroll(axes: Int): Boolean {
        val superScroll = super.startNestedScroll(axes)
        dispatchScrollState(RecyclerView.SCROLL_STATE_DRAGGING)
        return superScroll
    }

    override fun onNestedScroll(
        target: View,
        dxConsumed: Int,
        dyConsumed: Int,
        dxUnconsumed: Int,
        dyUnconsumed: Int,
        type: Int
    ) {
        super.onNestedScroll(target, dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed, type)
        android.util.Log.d(TAG, "🔄 onNestedScroll: dyConsumed=$dyConsumed, dyUnconsumed=$dyUnconsumed, scrollY=$scrollY")
    }

    /**
     * 分发滚动状态
     */
    private fun dispatchScrollState(state: Int) {
        if (mScrollListener != null && mState != state) {
            mScrollListener?.onStateChanged(state)
            android.util.Log.d(TAG, "滚动状态变化: $mState -> $state")
            mState = state
        }
    }
}
