<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/Background_L1">

    <!-- Fragment容器 - 模拟客户的HomeFragment -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/main_tab" />

    <!-- 模拟主应用的底部导航栏 -->
    <LinearLayout
        android:id="@+id/main_tab"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:background="?attr/Background_L1"
        android:elevation="8dp">

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?attr/Text_L3"
            android:alpha="0.2"
            android:layout_marginBottom="55dp" />

        <!-- 首页Tab - 当前选中 -->
        <LinearLayout
            android:id="@+id/tab_home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_home"
                android:tint="?attr/Button_Blue_Fill" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="首页"
                android:textColor="?attr/Button_Blue_Fill"
                android:textSize="10sp"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 行情Tab -->
        <LinearLayout
            android:id="@+id/tab_market"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_market"
                android:tint="?attr/Text_L3" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="行情"
                android:textColor="?attr/Text_L3"
                android:textSize="10sp"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 交易Tab -->
        <LinearLayout
            android:id="@+id/tab_trade"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_trade"
                android:tint="?attr/Text_L3" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="交易"
                android:textColor="?attr/Text_L3"
                android:textSize="10sp"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 资产Tab -->
        <LinearLayout
            android:id="@+id/tab_assets"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_assets"
                android:tint="?attr/Text_L3" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="资产"
                android:textColor="?attr/Text_L3"
                android:textSize="10sp"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 我的Tab -->
        <LinearLayout
            android:id="@+id/tab_profile"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_profile"
                android:tint="?attr/Text_L3" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="我的"
                android:textColor="?attr/Text_L3"
                android:textSize="10sp"
                android:layout_marginTop="2dp" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
